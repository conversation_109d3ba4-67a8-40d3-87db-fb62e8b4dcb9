<?php if($_settings->chk_flashdata('success')): ?>
<script>
	alert_toast("<?php echo $_settings->flashdata('success') ?>",'success')
</script>
<?php endif;?>

<style>
.container {
	width: 100%;
	padding: 20px;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.title {
	font-size: 1.5rem;
	color: #333;
}

.tools {
	margin-left: auto;
}

.btn {
	background-color: #007bff;
	color: white;
	padding: 10px 20px;
	border: none;
	border-radius: 5px;
	text-decoration: none;
	font-size: 14px;
}

.btn:hover {
	background-color: #0056b3;
}

.card-container {
	display: flex;
	flex-wrap: wrap;
	gap: 1rem;
}

.card {
	background-color: white;
	border: 1px solid #ddd;
	border-radius: 5px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	overflow: hidden;
	width: calc(33.333% - 1rem);
	margin-bottom: 1rem;
	transition: box-shadow 0.3s ease;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.card:hover {
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card-header {
	background-color: #007bff;
	color: white;
	padding: 10px;
	font-size: 16px;
}

.card-body {
	padding: 20px;
}

.description {
	font-size: 14px;
	color: #666;
	margin-bottom: 10px;
}

.truncate-1 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.badge {
	display: inline-block;
	padding: 5px 10px;
	border-radius: 15px;
	font-size: 12px;
	color: white;
}

.badge-success {
	background-color: #28a745;
}

.badge-danger {
	background-color: #dc3545;
}

.card-footer {
	padding: 10px;
	text-align: right;
	background-color: #f8f9fa;
}

.btn-danger {
	background-color: #dc3545;
	margin-left: 5px;
}

@media (max-width: 768px) {
	.card {
		width: calc(50% - 1rem);
	}
}

@media (max-width: 576px) {
	.card {
		width: 100%;
	}
}
</style>

<div class="container">
	<div class="header">
		<h3 class="title">List of Shop Types</h3>
		<div class="tools">
			<a href="javascript:void(0)" class="btn btn-primary" id="create_new"><span class="fas fa-plus"></span> Create New</a>
		</div>
	</div>
	<div class="card-container">
		<?php 
			$i = 1;
			$qry = $conn->query("SELECT * from shop_type_list where delete_flag = 0 order by name asc ");
			while($row = $qry->fetch_assoc()):
		?>
			<div class="card">
				<div class="card-header">
					Shop Type #<?php echo $i++; ?>
				</div>
				<div class="card-body">
					<p class="description truncate-1">Shop Type: <?php echo $row['name'] ?></p>
					<p>Date Created: <?php echo date("Y-m-d H:i",strtotime($row['date_created'])) ?></p>
					<p>Status: 
						<?php if($row['status'] == 1): ?>
							<span class="badge badge-success">Active</span>
						<?php else: ?>
							<span class="badge badge-danger">Inactive</span>
						<?php endif; ?>
					</p>
				</div>
				<div class="card-footer">
				<!--	<a href="javascript:void(0)" class="btn view_data" data-id="<?php echo $row['id'] ?>">View</a> -->
					<a href="javascript:void(0)" class="btn edit_data" data-id="<?php echo $row['id'] ?>">Edit</a>
					<a href="javascript:void(0)" class="btn btn-danger delete_data" data-id="<?php echo $row['id'] ?>">Delete</a>
				</div>
			</div>
		<?php endwhile; ?>
	</div>
</div>

<script>
	$(document).ready(function(){
		$('#create_new').click(function(){
			uni_modal('Add New Shop Type',"shop_types/manage_shop_type.php")
		})
		$('.edit_data').click(function(){
			uni_modal('Update Shop Type',"shop_types/manage_shop_type.php?id="+$(this).attr('data-id'))
		})
		$('.delete_data').click(function(){
			_conf("Are you sure to delete this Shop Type permanently?","delete_shop_type",[$(this).attr('data-id')])
		})
		$('table .th,table .td').addClass('align-middle px-2 py-1')
		$('.table').dataTable();
	})
	function delete_shop_type($id){
		start_loader();
		$.ajax({
			url:_base_url_+"classes/Master.php?f=delete_shop_type",
			method:"POST",
			data:{id: $id},
			dataType:"json",
			error:err=>{
				console.log(err)
				alert_toast("An error occured.",'error');
				end_loader();
			},
			success:function(resp){
				if(typeof resp== 'object' && resp.status == 'success'){
					location.reload();
				}else{
					alert_toast("An error occured.",'error');
					end_loader();
				}
			}
		})
	}
</script>