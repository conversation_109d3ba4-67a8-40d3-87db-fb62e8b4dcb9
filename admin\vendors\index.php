<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>List of Vendors</title>

    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .container-fluid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
        }

        .vendor-box {
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 20px;
            margin: 15px;
            width: 300px;
            text-align: center;
        }

        .vendor-box img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            object-position: center;
            border-radius: 50%;
            margin-bottom: 15px;
        }

        .vendor-info {
            margin-bottom: 15px;
        }

        .vendor-info h4 {
            margin: 10px 0;
            font-size: 1.3em;
            color: #333;
        }

        .vendor-info p {
            margin: 5px 0;
            font-size: 0.95em;
            color: #666;
        }

        .vendor-status {
            margin-bottom: 15px;
        }

        .badge-primary {
            background-color: #007bff;
            padding: 5px 10px;
            color: #fff;
            border-radius: 12px;
        }

        .badge-danger {
            background-color: #dc3545;
            padding: 5px 10px;
            color: #fff;
            border-radius: 12px;
        }

        .btn {
            padding: 5px 10px;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .btn-default {
            background-color: #6c757d;
        }

        .btn:hover {
            opacity: 0.9;
        }

        /* Search bar styles */
        #vendorSearch {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .vendor-box {
                width: 90%;
            }
        }

        @media (min-width: 769px) and (max-width: 1200px) {
            .vendor-box {
                width: 45%;
            }
        }
    </style>
</head>
<body>

<div class="card card-outline card-primary">
    <div class="card-header">
        <h3 class="card-title">List of Vendors</h3>
    </div>
    <div class="card-body">
        <!-- Search Bar -->
        <div style="margin: 1rem;">
            <input type="text" id="vendorSearch" placeholder="Search vendors by shop name, owner name, or contact...">
        </div>

        <div class="container-fluid" id="vendorContainer">
            <!-- Dynamic content populated via server-side code -->
            <?php 
            $qry = $conn->query("SELECT * from `vendor_list` where delete_flag = 0 order by shop_name asc ");
            while($row = $qry->fetch_assoc()): ?>
                <div class="vendor-box" 
                     data-shop-name="<?php echo strtolower($row['shop_name']) ?>" 
                     data-shop-owner="<?php echo strtolower($row['shop_owner']) ?>" 
                     data-contact="<?php echo strtolower($row['contact']) ?>">
                    <img src="<?php echo validate_image($row['avatar']) ?>" alt="vendor_avatar">
                    <div class="vendor-info">
                        <h4><?php echo ucwords($row['shop_name']) ?></h4>
                        <p>Owner: <?php echo ucwords($row['shop_owner']) ?></p>
                        <p>Code: <?php echo ($row['code']) ?></p>
                        <p>Contact: <?php echo ($row['contact']) ?></p>
                    </div>
                    <div class="vendor-status">
                        <?php if($row['status'] == 1): ?>
                            <span class="badge badge-primary">Active</span>
                        <?php else: ?>
                            <span class="badge badge-danger">Inactive</span>
                        <?php endif; ?>
                    </div>
                    <button type="button" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown">
                        Action
                    </button>
                    <div class="dropdown-menu">
                        <a class="dropdown-item" href="?page=vendors/manage_vendor&id=<?php echo $row['id'] ?>"><span class="fa fa-edit text-primary"></span> Edit</a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item delete_data" href="javascript:void(0)" data-id="<?php echo $row['id'] ?>"><span class="fa fa-trash text-danger"></span> Delete</a>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
</div>

<script>
    // Vendor search functionality
    document.getElementById('vendorSearch').addEventListener('input', function() {
        let searchVal = this.value.toLowerCase().trim(); // Get search value in lowercase
        let vendors = document.querySelectorAll('.vendor-box'); // Get all vendor boxes

        vendors.forEach(vendor => {
            let shopName = vendor.getAttribute('data-shop-name'); // Get shop name from each box
            let shopOwner = vendor.getAttribute('data-shop-owner'); // Get shop owner from each box
            let contact = vendor.getAttribute('data-contact'); // Get contact from each box

            // Check if search value matches any of the fields
            if (shopName.includes(searchVal) || shopOwner.includes(searchVal) || contact.includes(searchVal)) {
                vendor.style.display = ''; // Show vendor if matched
            } else {
                vendor.style.display = 'none'; // Hide vendor if not matched
            }
        });
    });

    // Delete vendor functionality
    document.querySelectorAll('.delete_data').forEach(function(button) {
        button.addEventListener('click', function() {
            let vendorId = this.getAttribute('data-id');
            if (confirm("Are you sure you want to delete this vendor permanently?")) {
                delete_vendor(vendorId);
            }
        });
    });

   function delete_vendor(id){
		start_loader();
		$.ajax({
			url:_base_url_+"classes/Users.php?f=delete_vendor",
			method:"POST",
			data:{id: id},
			dataType:"json",
			error:err=>{
				console.log(err)
				alert_toast("An error occurred.",'error');
				end_loader();
			},
			success:function(resp){
				if(typeof resp== 'object' && resp.status == 'success'){
					location.reload();
				}else{
					alert_toast("An error occurred.",'error');
					end_loader();
				}
			}
		})
	}
</script>

</body>
</html>
