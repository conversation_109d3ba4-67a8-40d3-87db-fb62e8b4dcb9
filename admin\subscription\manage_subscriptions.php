<?php
require_once('./../../config.php');
if(isset($_GET['id']) && $_GET['id'] > 0){
    $qry = $conn->query("SELECT * from `subscriptions` where id = '{$_GET['id']}' and delete_flag = 0 ");
    if($qry->num_rows > 0){
        foreach($qry->fetch_assoc() as $k => $v){
            $$k = $v;
        }
    }else{
?>
		<center>Unknown Subscription</center>
		<style>
			#uni_modal .modal-footer{
				display:none
			}
		</style>
		<div class="text-right">
			<button class="btn btndefault bg-gradient-dark btn-flat" data-dismiss="modal"><i class="fa fa-times"></i> Close</button>
		</div>
		<?php
		exit;
		}
}
?>

<div class="container-fluid">
	<form action="" id="subscription-form">
		<input type="hidden" name="id" value="<?php echo isset($id) ? $id : '' ?>">
		<div class="form-group">
			<label for="name" class="control-label">Name</label>
			<input name="name" id="name" type="text" class="form-control form-control-sm form-control-border" 
                   value="<?php echo isset($name) ? $name : ''; ?>" required>
		</div>
		
		<div class="form-group">
			<label for="old_price" class="control-label">Old Price</label>
			<input name="old_price" id="old_price" type="number" class="form-control form-control-sm form-control-border" 
                   step="0.01" value="<?php echo isset($old_price) ? $old_price : ''; ?>">
		</div>
			<div class="form-group">
			<label for="discount" class="control-label">Discount</label>
			<input name="discount" id="discount" type="text" class="form-control form-control-sm form-control-border" 
                   value="<?php echo isset($discount) ? $discount : ''; ?>" required>
		</div>
		
		
	
		
		<div class="form-group">
			<label for="price" class="control-label">Price</label>
			<input name="price" id="price" type="number" class="form-control form-control-sm form-control-border" 
                   step="0.01" value="<?php echo isset($price) ? $price : ''; ?>" required>
		</div>
		<div class="form-group">
			<label for="features" class="control-label">Features</label>
			<textarea name="features" id="features" class="form-control form-control-sm form-control-border" 
                      rows="4" required><?php echo isset($features) ? $features : ''; ?></textarea>
		</div>
		<div class="form-group">
			<label for="expiry_days" class="control-label">Expiry Days</label>
			<input name="expiry_days" id="expiry_days" type="number" class="form-control form-control-sm form-control-border" 
                   min="1" value="<?php echo isset($expiry_days) ? $expiry_days : ''; ?>" required>
		</div>
		
		<!-- <button type="submit" class="btn btn-primary">Save</button>  -->
	</form>
</div>

<script>
	$(document).ready(function(){
		$('#uni_modal #subscription-form').submit(function(e){
			e.preventDefault();
            var _this = $(this);
			$('.err-msg').remove();
			if (_this[0].checkValidity() == false) {
				_this[0].reportValidity();
				return false;
			}
			var el = $('<div>');
			el.addClass("alert err-msg").hide();
			start_loader();
			$.ajax({
				url: _base_url_ + "classes/Master.php?f=save_subscription",
				data: new FormData($(this)[0]),
                cache: false,
                contentType: false,
                processData: false,
                method: 'POST',
                type: 'POST',
                dataType: 'json',
				error: err => {
					console.error(err);
					el.addClass('alert-danger').text("An error occurred");
					_this.prepend(el);
					el.show('.modal');
					end_loader();
				},
				success: function(resp) {
					if (typeof resp == 'object' && resp.status == 'success') {
						location.reload();
					} else if (resp.status == 'failed' && !!resp.msg) {
						el.addClass('alert-danger').text(resp.msg);
						_this.prepend(el);
						el.show('.modal');
					} else {
						el.text("An error occurred");
						console.error(resp);
					}
					$("html, body").scrollTop(0);
					end_loader();
				}
			});
		});
	});
</script>
