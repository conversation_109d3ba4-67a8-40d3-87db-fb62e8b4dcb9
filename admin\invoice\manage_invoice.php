<?php
require_once('./../../config.php');
if(isset($_GET['id']) && $_GET['id'] > 0){
    $qry = $conn->query("SELECT * from `invoices_tbl` where id = '{$_GET['id']}' ");
    if($qry->num_rows > 0){
        foreach($qry->fetch_assoc() as $k => $v){
            $$k = $v;
        }
    } else {  
?>
        <center>Unknown</center>
        <style>
            #uni_modal .modal-footer {
                display: none;
            }
        </style>
        <div class="text-right">
            <button class="btn btndefault bg-gradient-dark btn-flat" data-dismiss="modal"><i class="fa fa-times"></i> Close</button>
        </div>
        <?php
        exit;
    }
}
?>

<div class="container-fluid">
    <form action="" id="invoice-form">
        <input type="hidden" name="id" value="<?php echo isset($id) ? $id : '' ?>">
        <input type="hidden" name="id" value="<?= $_settings->userdata('id') ?>">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="invoice_code" class="control-label">Invoice Code</label>
                    <input name="invoice_code" id="invoice_code" type="text" class="form-control form-control-sm form-control-border" value="<?php echo isset($invoice_code) ? $invoice_code : ''; ?>" required readonly>
                </div>
                <div class="form-group">
                    <label for="customer" class="control-label">Customer Name</label>
                    <input name="customer" id="customer" type="text" class="form-control form-control-sm form-control-border" value="<?php echo isset($customer) ? $customer : ''; ?>" required readonly>
                </div>
                <div class="form-group">
                    <label for="cashier" class="control-label">Cashier</label>
                    <input name="cashier" id="cashier" type="text" class="form-control form-control-sm form-control-border" value="<?php echo isset($cashier) ? $cashier : ''; ?>" required readonly>
                </div>
                <div class="form-group">
                    <label for="total_amount" class="control-label">Total Amount</label>
                    <input name="total_amount" id="total_amount" type="number" step="any" class="form-control form-control-sm form-control-border" value="<?php echo isset($total_amount) ? $total_amount : ''; ?>" required readonly>
                </div>
                <div class="form-group">
                    <label for="discount_percentage" class="control-label">Discount Percentage</label>
                    <input name="discount_percentage" id="discount_percentage" type="number" step="any" class="form-control form-control-sm form-control-border" value="<?php echo isset($discount_percentage) ? $discount_percentage : ''; ?>" required readonly>
                </div>
            </div>
           
            <div class="col-md-6">
                <div class="form-group">
                    <label for="discount_amount" class="control-label">Discount Amount</label>
                    <input name="discount_amount" id="discount_amount" type="number" step="any" class="form-control form-control-sm form-control-border" value="<?php echo isset($discount_amount) ? $discount_amount : ''; ?>" required readonly>
                </div>
                <div class="form-group">
                    <label for="tendered_amount" class="control-label">Paid Amount</label>
                    <input name="tendered_amount" id="tendered_amount" type="number" step="any" class="form-control form-control-sm form-control-border" value="<?php echo isset($tendered_amount) ? $tendered_amount : ''; ?>" required >
                </div>
                <div class="form-group">
                    <label for="created_at" class="control-label">Created At</label>
                    <input name="created_at" id="created_at" type="text" class="form-control form-control-sm form-control-border" value="<?php echo isset($created_at) ? $created_at : ''; ?>" readonly>
                </div>
                <!-- <div class="form-group">
                    <label for="updated_at" class="control-label">Updated At</label>
                    <input name="updated_at" id="updated_at" type="datetime" class="form-control form-control-sm form-control-border" value="<?php echo isset($updated_at) ? $updated_at : ''; ?>" >
                </div> -->
            </div>
        </div>
    </form>
</div>

<script>
$(document).ready(function () {
    $('#uni_modal #invoice-form').submit(function (e) {
        e.preventDefault();
        var _this = $(this);
        $('.err-msg').remove();
        if (_this[0].checkValidity() == false) {
            _this[0].reportValidity();
            return false;
        }
        var el = $('<div>')
        el.addClass("alert err-msg.")
        el.hide()
        start_loader();
        $.ajax({
            url: _base_url_ + "classes/Master.php?f=save_invoice",
            data: new FormData($(this)[0]),
            cache: false,
            contentType: false,
            processData: false,
            method: 'POST',
            type: 'POST',
            dataType: 'json',
            error: function (xhr, status, error) {
                console.error("Error Status: " + status);
                console.error("Error Thrown: " + error);
                console.error("Response Text: " + xhr.responseText);
                el.addClass('alert-danger').text("An error occurred..."+ status+ error+ xhr.responseText);
                _this.prepend(el)
                el.show('.modal')
                end_loader();
            },
            success: function (resp) {
                if (typeof resp == 'object' && resp.status == 'success') {
                    location.reload();
                } else if (resp.status == 'failed' && !!resp.msg) {
                    el.addClass('alert-danger').text(resp.msg);
                    _this.prepend(el)
                    el.show('.modal')
                } else {
                    el.text("An error occurred.");
                    console.error(resp);
                }
                $("html, body").scrollTop(0);
                end_loader();
            }
        });
    });
});
</script>
