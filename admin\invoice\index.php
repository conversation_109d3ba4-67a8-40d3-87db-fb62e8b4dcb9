<?php if($_settings->chk_flashdata('success')): ?>
<script>
	alert_toast("<?php echo $_settings->flashdata('success') ?>",'success')
</script>
<?php endif;?> 

<style>
body {
	font-family: Arial, sans-serif;
}


.container {
	width: 100%;
	padding: 20px;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.title {
	font-size: 1.5rem;
	color: #333;
}

.tools {
	margin-left: auto;
}

.btn {
	background-color: #007bff;
	color: white;
	padding: 10px 20px;
	border: none;
	border-radius: 5px;
	text-decoration: none;
	font-size: 14px;
}

.btn:hover {
	background-color: #0056b3;
}

.table-container {
	width: 100%;
	overflow-x: auto;
}

table {
	width: 100%;
	border-collapse: collapse;
}

th, td {
	padding: 10px;
	text-align: left;
	border-bottom: 1px solid #ddd;
}

th {
	background-color: #f8f9fa;
	color: #333;
	font-weight: bold;
}

tr:nth-child(even) {
	background-color: #f2f2f2;
}

.badge {
	display: inline-block;
	padding: 5px 10px;
	border-radius: 15px;
	font-size: 12px;
	color: white;
}

.badge-success {
	background-color: #28a745;
}

.badge-danger {
	background-color: #dc3545;
}

.btn-flat {
	padding: 5px 10px;
	border-radius: 5px;
}

.dropdown-menu {
	background-color: #fff;
	border: 1px solid #ddd;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
	padding: 10px 15px;
	color: #333;
	text-decoration: none;
	display: block;
}

.dropdown-item:hover {
	background-color: #f8f9fa;
}

@media (max-width: 768px) {
	.container {
		padding: 10px;
	}

	.header {
		flex-direction: column;
		align-items: flex-start;
	}

	.table-container {
		overflow-x: auto;
	}
}
</style>

<div class="container">
	<div class="header">
		<h3 class="title">Invoice Reports</h3>
		<div class="tools">
			
			<a href="php-invoice/" class="btn btn-primary" id="create_new"><span class="fas fa-plus"></span>  Create New Invoice</a>
		</div>
	</div>
	<div class="table-container">
		<table class="table table-bordered table-stripped">
			<colgroup>
				<col width="20%">
				<col width="15%">
				<col width="20%">
				<col width="15%">
				<col width="15%">
				<col width="15%">
				<col width="15%">
			</colgroup>
			<thead>
				<tr class="bg-gradient-secondary">
					<th>Vendor Id</th>
					<th>Customer Name</th>		
					<th>Total Amount</th>
					<th>Paid Amount</th>
					<th>discount_amount</th>
					<th>Pending Amount</th>
					<th>Status</th>
					<th>Action</th>
				</tr>
			</thead>
			<tbody>
			


				<?php 
				
$sql = "SELECT customer,cashier, SUM(total_amount) AS total_amount, SUM(tendered_amount) AS paid_amount, SUM(discount_amount) AS discount_amount 
				FROM invoices_tbl 
				
				GROUP BY customer";
$result = $conn->query($sql);
while ($row = $result->fetch_assoc()) :
	$pending_amount = $row['total_amount'] - $row['paid_amount']-$row['discount_amount']; // Calculate pending amount

				// $qry = $conn->query("SELECT * FROM `invoices_tbl` WHERE  `cashier` = '{$_settings->userdata('id')}'");
				// while($row = $qry->fetch_assoc()):
				?>
				<tr>
					<td class="text-center"><?php echo $row['cashier'] ?></td>
					<td class="text-center"><?php echo $row['customer'] ?></td>
					<td class="text-center"><?php echo format_num($row['total_amount']) ?></td>
					
					<td class="text-right"><?php echo number_format($row['paid_amount'], 2) ?></td>
					<td class="text-right"><?php echo number_format($row['discount_amount'], 2) ?></td>
				
					<td class="text-right"><?php echo $pending_amount ?></td>
					<td class="text-center">
						<?php if($row['paid_amount'] ==$row['total_amount'] || $pending_amount ==0  ): ?>
							<span class="badge badge-success bg-gradient-success px-3 rounded-pill">Paid</span>
						<?php elseif($row['paid_amount'] == 0): ?>
							<span class="badge badge-danger bg-gradient-danger px-3 rounded-pill">Unpaid</span>
						<?php elseif($row['paid_amount'] >0 || $pending_amount ==0 ): ?>
							<span class="badge badge-danger bg-gradient-danger px-3 rounded-pill">Partial</span>
						<?php else: ?>
							<span class="badge badge-danger bg-gradient-danger px-3 rounded-pill">Pending</span>
						<?php endif; ?>
					</td>
					<td align="center">
					<a class="dropdown-item view_data" href="javascript:void(0)" data-id="<?php echo $row['customer'] ?>"><span class="fa fa-eye text-dark"></span> View</a>
						<!-- <button type="button" class="btn btn-flat btn-default btn-sm dropdown-toggle dropdown-icon" data-toggle="dropdown">
							Action
							<span class="sr-only">Toggle Dropdown</span>
						</button>

						<div class="dropdown-menu" role="menu"> -->
							<!-- <a class="dropdown-item view_data" href="javascript:void(0)" data-id="<?php echo $row['customer'] ?>"><span class="fa fa-eye text-dark"></span> View</a> -->
							<!-- <div class="dropdown-divider"></div>
							<a class="dropdown-item edit_data" href="javascript:void(0)" data-id="<?php echo $row['id'] ?>"><span class="fa fa-edit text-primary"></span> Edit</a> -->
							<!-- Uncomment the following lines if you want to enable the delete option -->
							<!--
							<div class="dropdown-divider"></div>
							<a class="dropdown-item delete_data" href="javascript:void(0)" data-id="<?php echo $row['id'] ?>"><span class="fa fa-trash text-danger"></span> Delete</a>
							-->
						</div>
					</td>
				</tr>
				<?php endwhile; ?>
				
			</tbody>
		</table>
	</div>
</div>

<script>
	$(document).ready(function(){
		$('#create_new').click(function(){
			
			uni_modal('Add New Customer',"php-invoice\index.php",'large')
		})
		$('.view_data').click(function(){
			uni_modal('View Invoice Details',"invoice/view_customers.php?customer="+$(this).attr('data-id'),'large')
		})
		$('.edit_data').click(function(){
			uni_modal('Update Invoice',"invoice/manage_invoice.php?id="+$(this).attr('data-id'))
		})
		$('.delete_data').click(function(){
			_conf("Are you sure to delete this invoice permanently?","delete_customers",[$(this).attr('data-id')])
		})
		$('table th,table td').addClass('align-middle px-2 py-1')
		$('.table').dataTable();
	})
	function delete_customer($id){
		start_loader();
		$.ajax({
			url:_base_url_+"classes/Master.php?f=delete_customer",
			method:"POST",
			data:{id: $id},
			dataType:"json",
			error:err=>{
				console.log(err)
				alert_toast("An error occurred.",'error');
				end_loader();
			},
			success:function(resp){
				if(typeof resp== 'object' && resp.status == 'success'){
					location.reload();
				}else{
					alert_toast("An error occurred.",'error');
					end_loader();
				}
			}
		})
	}
</script>
