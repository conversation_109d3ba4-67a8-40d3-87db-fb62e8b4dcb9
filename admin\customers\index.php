<?php if ($_settings->chk_flashdata('success')) : ?>
<script>
	alert_toast("<?php echo $_settings->flashdata('success'); ?>", 'success');
</script>
<?php endif; ?>

<style>
	.product-img {
		width: calc(100%);
		height: auto;
		max-width: 5em;
		object-fit: scale-down;
		object-position: center center;
	}

	.container {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		padding: 1rem;
	}

	.card {
		border: 1px solid #ddd;
		border-radius: 0.5rem;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		overflow: hidden;
		width: calc(33.333% - 1rem); /* 3 cards per row */
		margin-bottom: 1rem;
		transition: box-shadow 0.3s ease;
	}

	.card:hover {
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
	}

	.card-header {
		background-color: #007bff;
		color: white;
		padding: 0.75rem 1rem;
		font-size: 1.25rem;
	}

	.card-body {
		padding: 1rem;
	}

	.card-body img {
		width: 100%;
		height: auto;
		max-width: 6rem;
		object-fit: cover;
		border-radius: 50%;
		border: 2px solid #007bff;
	}

	.card-footer {
		background-color: #f8f9fa;
		padding: 0.75rem 1rem;
		text-align: right;
	}

	.btn {
		display: inline-block;
		padding: 0.5rem 1rem;
		border: none;
		border-radius: 0.25rem;
		color: white;
		background-color: #007bff;
		text-align: center;
		text-decoration: none;
		font-size: 0.875rem;
	}

	.btn-danger {
		background-color: #dc3545;
	}

	@media (max-width: 768px) {
		.card {
			width: calc(50% - 1rem); /* 2 cards per row */
		}
	}

	@media (max-width: 576px) {
		.card {
			width: 100%; /* 1 card per row */
		}
	}
</style>

<div class="container">
	<!-- Search Bar -->
	<div style="margin: 1rem; width: 100%;">
		<input type="text" id="customerSearch" placeholder="Search customers by name, phone number, Aadhar number, or PAN number..." style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 0.25rem; margin-bottom: 1rem;">
	</div>

	<!-- Customer Cards -->
	<?php 
		$qry = $conn->query("SELECT * FROM customers_list WHERE delete_flag = 0");
		while ($row = $qry->fetch_assoc()) :
	?>
		<div class="card" data-customer-name="<?php echo strtolower($row['name']); ?> <?php echo $row['mobileno']; ?> <?php echo $row['altmobileno']; ?> <?php echo $row['aadhar_number']; ?> <?php echo $row['pan_number']; ?>">
			<div class="card-header">
				Customer ID: <?php echo format_num($row['customer_id']); ?>
			</div>
			<div class="card-body">
				<img src="<?= validate_image($row['image_path']); ?>" alt="Customer Image">
				<h5><?php echo $row['name']; ?></h5>
				<p>Mobile No: <?php echo $row['mobileno']; ?></p>
				<p>Alt Mobile No: <?php echo $row['altmobileno']; ?></p>
				<p>Status: 
					<?php if ($row['status'] == 1) : ?>
						<span class="badge badge-success">Active</span>
					<?php else : ?>
						<span class="badge badge-danger">Inactive</span>
					<?php endif; ?>
				</p>
			</div>
			<div class="card-footer">
				<a href="javascript:void(0)" class="btn view_data" data-id="<?php echo $row['id']; ?>">View</a>
				<a href="javascript:void(0)" class="btn edit_data" data-id="<?php echo $row['id']; ?>">Edit</a>
				<a href="javascript:void(0)" class="btn btn-danger delete_data" data-id="<?php echo $row['id']; ?>">Delete</a>
			</div>
		</div>
	<?php endwhile; ?>
</div>

<script>
	// Filter customer cards based on exact word matches in search input
	document.getElementById('customerSearch').addEventListener('input', function() {
		const searchValue = this.value.toLowerCase().trim();
		const searchWords = searchValue.split(/\s+/); // Split input into words
		const customerCards = document.querySelectorAll('.card');

		customerCards.forEach(card => {
			const customerName = card.getAttribute('data-customer-name');
			// Check if all words from search input are found in customer data
			const matches = searchWords.every(word => customerName.includes(word));

			if (matches) {
				card.style.display = ''; // Show matching cards
			} else {
				card.style.display = 'none'; // Hide non-matching cards
			}
		});
	});

	// Existing functionalities for View, Edit, and Delete actions
	$(document).ready(function() {
		$('.view_data').click(function() {
			uni_modal('View Customer Details', "customers/view_customers.php?id=" + $(this).attr('data-id'), 'large');
		});

		$('.edit_data').click(function() {
			uni_modal('Update Customer', "customers/manage_customers.php?id=" + $(this).attr('data-id'), 'large');
		});

		$('.delete_data').click(function() {
			_conf("Are you sure to delete this customer permanently?", "delete_customers", [$(this).attr('data-id')]);
		});
	});

	// Delete customer function
	function delete_customers(id) {
		start_loader();
		$.ajax({
			url: _base_url_ + "classes/Master.php?f=delete_customers",
			method: "POST",
			data: { id: id },
			dataType: "json",
			error: err => {
				console.log(err);
				alert_toast("An error occurred.", 'error');
				end_loader();
			},
			success: function(resp) {
				if (typeof resp == 'object' && resp.status == 'success') {
					location.reload();
				} else {
					alert_toast("An error occurred.", 'error');
					end_loader();
				}
			}
		});
	}
</script>
