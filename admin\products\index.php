<?php if($_settings->chk_flashdata('success')): ?>
<script>
	alert_toast("<?php echo $_settings->flashdata('success') ?>",'success')
</script>
<?php endif;?>
<style>
    .container {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        padding: 1rem;
    }

    .card {
        border: 1px solid #ddd;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: calc(33.333% - 1rem); /* 3 cards per row */
        margin-bottom: 1rem;
        transition: box-shadow 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .card-header {
        background-color: #007bff;
        color: white;
        padding: 0.75rem 1rem;
        font-size: 1.25rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-body img {
        width: 100%;
        height: auto;
        max-width: 6rem;
        object-fit: cover;
        border-radius: 0.25rem;
        border: 2px solid #007bff;
    }

    .card-footer {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        text-align: right;
    }

    .btn {
        display: inline-block;
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.25rem;
        color: white;
        background-color: #007bff;
        text-align: center;
        text-decoration: none;
        font-size: 0.875rem;
        margin: 0.25rem;
    }

    .btn-danger {
        background-color: #dc3545;
    }

    @media (max-width: 768px) {
        .card {
            width: calc(50% - 1rem); /* 2 cards per row */
        }
    }

    @media (max-width: 576px) {
        .card {
            width: 100%; /* 1 card per row */
        }
    }

    /* Search bar styles */
    #productSearch {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }
</style>

<div class="card-header">
	<h3 class="card-title">List of Products</h3>
</div>

<!-- Search Bar -->
<div style="margin: 1rem;">
    <input type="text" id="productSearch" placeholder="Enter Product Name or ID to Search...">
</div>

<div class="container">
	<?php 
		$qry = $conn->query("SELECT p.*,v.code, v.shop_name as vendor from product_list p inner join vendor_list v on p.vendor_id = v.id where p.delete_flag = 0 order by p.name asc ");
		while($row = $qry->fetch_assoc()):
	?>
	<div class="card" data-product-id="<?php echo $row['id']; ?>" data-product-name="<?php echo strtolower($row['name']); ?>">
		<div class="card-header">
			Product #<?php echo format_num($row['id']) ?>
		</div>
		<div class="card-body">
			<img src="<?= validate_image($row['image_path']) ?>" alt="Product Image" class="img-thumbnail">
			<h5><?= $row['code'].'-'.$row['vendor'] ?></h5>
			<p><?= $row['name'] ?></p>
			<p>Cost: <?php echo format_num($row['price']) ?></p>
			<p>Status: 
				<?php if($row['status'] == 1): ?>
					<span class="badge badge-success">Active</span>
				<?php else: ?>
					<span class="badge badge-danger">Inactive</span>
				<?php endif; ?>
			</p>
		</div>
		<div class="card-footer">
			<a href="javascript:void(0)" class="btn view_data" data-id="<?php echo $row['id'] ?>">View</a>
			<a href="javascript:void(0)" class="btn btn-danger delete_data" data-id="<?php echo $row['id'] ?>">Delete</a>
		</div>
	</div>
	<?php endwhile; ?>
</div>

<script>
    $(document).ready(function(){
        // Modal handling for create, view, and edit
        $('#create_new').click(function(){
            uni_modal('Add New Product', "products/manage_product.php", 'large');
        });

        $('.view_data').click(function() {
            uni_modal('View Product Details', "products/view_product.php?id=" + $(this).attr('data-id'), 'large');
        });

        // Search functionality using product ID or name
        $('#productSearch').on('input', function() {
            var searchVal = $(this).val().toLowerCase().trim(); // Get search value
            var hasMatch = false; // Track if any match is found

            $('.card').each(function() {
                var productId = $(this).data('product-id').toString(); // Get the product ID of each card
                var productName = $(this).data('product-name'); // Get the product name of each card
                
                if (searchVal === productId || searchVal === productName) { // Exact match with the search value
                    $(this).show();
                    hasMatch = true;
                } else {
                    $(this).hide();
                }
            });

            // Show a message if no products match the entered ID or name
            if (!hasMatch && searchVal !== '') {
                if (!$('.no-results').length) {
                    $('.container').append('<div class="no-results" style="width: 100%; text-align: center; color: #666;">No matching product found</div>');
                }
            } else {
                $('.no-results').remove(); // Remove the no-results message if a match is found
            }
        });

        // Deleting product functionality
        $('.delete_data').click(function(){
            _conf("Are you sure to delete this product permanently?","delete_product",[$(this).attr('data-id')])
        });
    });

   function delete_product($id){
		start_loader();
		$.ajax({
			url:_base_url_+"classes/Master.php?f=delete_product",
			method:"POST",
			data:{id: $id},
			dataType:"json",
			error:err=>{
				console.log(err);
				alert_toast("An error occurred.",'error');
				end_loader();
			},
			success:function(resp){
				if(typeof resp== 'object' && resp.status == 'success'){
					location.reload();
				}else{
					alert_toast("An error occurred.",'error');
					end_loader();
				}
			}
		})
	}
</script>
