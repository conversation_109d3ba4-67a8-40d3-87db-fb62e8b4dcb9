<?php 
require_once('../../config.php'); 
$vendor_id = $_settings->userdata('id');
$action = $_GET['action'] ?? "";
switch($action){
    case 'logout':
        session_destroy();
        header("location: login.php");
        break;
    case 'update_settings':
        $insert_batch_values = "";
        $error = "";
        try{
            foreach($_POST as $field => $value){
                if(!is_numeric($value))
                    $value = addslashes(htmlspecialchars($value));
                $check_field = $conn->query("SELECT * FROM `settings_tbl` where meta_field = '{$field}'");
                if($check_field->num_rows > 0){
                    $result = $check_field->fetch_array();
                    $id = $result['id'];
                    $update = $conn->query("UPDATE `settings_tbl` set `meta_value` = '{$value}' where `id` = {$id}");
                }else{
                    if(!empty($insert_batch_values)) $insert_batch_values .= ", ";
                    $insert_batch_values .= "('{$field}', '{$value}')";
                }
            }
            if(!empty($insert_batch_values)){
                $insert_batch_stmt = "INSERT INTO `settings_tbl` (`meta_field`, `meta_value`) VALUES {$insert_batch_values}";
                $insert_batch_qry = $conn->query($insert_batch_stmt);
            }
        }catch(Exception $e){
            $error = $e->getMessage();
        }
        if(empty($error)){
            $_SESSION['flashdata'] = [
                "type" => 'success',
                "msg" => 'Invoice Data Settings has been updated successfully'
            ];
        }else{
            $_SESSION['flashdata'] = [
                "type" => 'danger',
                "msg" => $error
            ];
        }
        header("location: ./");
        exit;
        
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $_settings->info('title') != false ? $_settings->info('title').' | ' : '' ?><?php echo $_settings->info('name') ?> - Vendor Side</title>
    <link rel="icon" href="<?php echo validate_image($_settings->info('logo')) ?>" />
    <!-- Fontawesome CSS CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Bootstrap CSS CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">

    <!-- Select2 for Enhanced Dropdowns -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0-beta.1/css/select2.min.css" rel="stylesheet">

    <!-- Original Styles -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Professional Modern UI Styles -->
    <link rel="stylesheet" href="professional-invoice-ui.css">

    <!-- Fontawesome CSS CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/js/all.min.js" integrity="sha512-uKQ39gEGiyUJl4AI6L+ekBdGKpGw4xJ55+xyJG7YFlJokPNYegn9KwQ3P8A7aFQAUtUsAQHep+d/lrGqrbPIDQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- jQuery CSS CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <!-- Bootstrap CSS CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>


    <!-- Select2 JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0-beta.1/js/select2.min.js"></script>
<script>
   
// fatch customers
$(document).ready(function () {
            // Initialize Select2 for customers
            $('#customerOptions').select2({
                placeholder: "Select a customer",
                allowClear: true
            }).on('change', function () {
                const selected = $(this).find(':selected');
                $('#customer_id').val(selected.data('id') || "");
                $('#customer').val(selected.data('name') || "");
            });

            // Initialize Select2 for products
            $('#productOptions').select2({
                placeholder: "Select a product",
                allowClear: true
            }).on('change', function () {
                const selected = $(this).find(':selected');
                $('#item').val(selected.data('id') || "");
                $('#unit').val(selected.data('name') || "");
                $('#price').val(selected.data('price') || "");
            });
        });
    
</script>


</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-dark" data-bs-theme="dark">
    <div class="container-fluid">
    <a href="<?php echo base_url ?>vendor/" class="">
        <img src="<?php echo validate_image($_settings->info('logo'))?>" alt="Store Logo" class="brand-image img-circle elevation-3" style="opacity: .8;width: 1.6rem;height: 1.6rem;max-height: unset">
       
        </a>
        <!-- <a class="navbar-brand" href="#">Simple Invoice in PHP</a> -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a class="nav-link active" aria-current="page" href="../">Home</a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" aria-current="page" href="javascript:void(0)" id="settingModalBtn">Settings</a>
                </li>
                <li class="nav-item dropdown">
                <a href="<?php echo base_url ?>vendor/?page=customers" class="nav-link nav-customers">
                  
                  <p>Add New Customer </p>
                </a>
              </li>
            </ul>
        </div>
        <div id="user-container" class="dropdown">
            <button  type="button" class="dropdow-toggle btn btn-sm btn-info text-light rounded-pill fw-bold fs-6 px-4"  data-bs-toggle="dropdown" aria-expanded="false"><?= $_settings->userdata('shop_name') ?? "Not Logged In" ?> <i class="fas fa-angle-down"></i></button>
        </div>
    </div> 
    </nav>
    <div class="container-md py-3">
        <?php if(isset($_SESSION['flashdata']) && !empty($_SESSION['flashdata'])): ?>
            <div class="flashdata flashdata-<?= $_SESSION['flashdata']['type'] ?? 'default' ?> mb-3">
                <div class="d-flex w-100 align-items-center flex-wrap">
                    <div class="col-11"><?= $_SESSION['flashdata']['msg'] ?? '' ?></div>
                    <div class="col-1 text-center">
                        <a href="javascript:void(0)" onclick="this.closest('.flashdata').remove()" class="flashdata-close"><i class="far fa-times-circle"></i></a>
                    </div>
                </div>
            </div>
        <?php unset($_SESSION['flashdata']);
        

       
        ?>
        <?php endif; ?>
        <form action="save_invoice.php" id="order-form" method="POST">
            <input type="hidden" name="cashier" value="<?= $_settings->userdata('id') ?? "" ?>">
            <input type="hidden" name="total_amount" value="0">
            <input type="hidden" name="discount_amount" value="0">
            <input type="hidden" name="paydate" value="0">
        <div class="row">
            <div class="col-lg-4 col-md-5 col-sm-12 col-12">
                <div class="card shadow">
                    <div class="card-header rounded-0">
                        <div class="card-title">Order Form</div>
                    </div>
                    <div class="card-body rounded-0">
                        <div class="container-fluid">
                            <div class="mb-3">
<?php 
 $sql = "SELECT id FROM invoices_tbl ORDER BY id DESC LIMIT 1";
 $result = $conn->query($sql);
 
 if ($result->num_rows > 0) {
     // Get the last invoice ID
     $row = $result->fetch_assoc();
     $lastInvoiceID = $row['id'];
 } else {
     // If there are no records, start with 1
     $lastInvoiceID = 0;
 }
 
 // Increment the invoice ID by 1
 $newInvoiceID = $lastInvoiceID + 1;
?>


                                <label for="invoice_code" class="form-label">Invoice Code</label>
                                <input type="text" class="form-control rounded-0" name="invoice_code" id="invoice_code" value="<?php echo $newInvoiceID; ?>" readonly >
                            </div>
                            <?php 
$customerOptions = '';
$sql = "SELECT customer_id, name FROM customers_list where vendor_id = '{$vendor_id}' and  delete_flag = 0 and status = 1 ";
$result = $conn->query($sql);
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $customerOptions .= '<option value="' . $row["customer_id"] . '" data-id="' . $row["customer_id"] . '" data-name="' . $row["name"] . '">' . $row["customer_id"] . ' - ' . $row["name"] . '</option>';
    }
} else {
    $customerOptions = '<option value="">No customers found</option>';
}

// Fetch product data
$productOptions = '';
$sql = "SELECT product_id, name, price FROM product_list WHERE vendor_id = '{$vendor_id}'   and delete_flag = 0 ";
$result = $conn->query($sql);
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $productOptions .= '<option value="' . $row["product_id"] . '" data-id="' . $row["product_id"] . '" data-name="' . $row["name"] . '" data-price="' . $row["price"] . '">' . $row["product_id"] . ' - ' . $row["name"] . ' - ' . $row["price"] . '</option>';
    }
} else {
    $productOptions = '<option value="">No products found</option>';
}
        ?>
        <!-- Dropdown Selector -->
        <label for="customerOptions">Choose a customer:</label>
        <select id="customerOptions" name="customerOptions" style="width: 100%;">
            <option value="" data-id="" data-name=""></option>
            <?= $customerOptions; ?>
        </select>
        <br><br>

        <!-- Input Fields -->
        <div class="mb-3"> 
        <label for="customer_id" class="form-label">Customer Id</label>
        <input type="text" class="form-control rounded-0" id="customer_id" name="user_id" placeholder="ID will appear here" readonly>
        </div>
        
        <label for="customer_name" class="form-label">Customer Name</label>
        <input type="text" class="form-control rounded-0" id="customer" name="customer" placeholder="Name will appear here" readonly>
        <br><br>

        



<!--                             
                            <div class="mb-3">
    <label for="customer_id" class="form-label">Customer Id</label>
    <input type="text" class="form-control rounded-0" name="customer_id " id="customer_id" required="required">
</div>

<div class="mb-3">
    <label for="customer_name" class="form-label">Customer Name</label>
    <input type="text" class="form-control rounded-0" name="customer" id="customer" required="required" readonly>
</div> -->

                            <hr>
                            <label for="" class="form-label text-body-emphasis d-block text-center">Item Form</label>


                            
        <label for="productOptions">Choose a product:</label>
        <select id="productOptions" name="productOptions" style="width: 100%;">
            <option value="" data-id="" data-name="" data-price=""></option>
            <?= $productOptions; ?>
        </select>
        <br><br>











                            <div class="mb-3">
    <label for="item" class="form-label">Product Id</label>
    <input type="text" class="form-control rounded-0" id="item" name="item" placeholder="ID will appear here" readonly>
</div>
<div class="row">
    <div class="col-6">
        <div class="mb-3">
            <input type="text" class="form-control rounded-0" id="unit" value="" readonly>
            <label for="name" class="form-label d-block text-center"><small>Name</small></label>
        </div>
    </div>
    <div class="col-6">
        <div class="mb-3">
            <input type="number" class="form-control rounded-0 text-center" id="qty" value="1">
            <label for="qty" class="form-label d-block text-center"><small>QTY</small></label>
        </div>
    </div>
</div>
<div class="mb-3">
    <label for="price" class="form-label">Price</label>
    <input type="number" step="any" class="form-control rounded-0 text-end" id="price" readonly>
</div>

                            <div class="d-flex justify-content-center w-100">
                                <button class="btn btm-sm rounded btn-primary" type="button" id="add_item"><i class="far fa-plus-square"></i> Add Item</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-8 col-md-7 col-sm-12 col-12">
                <div class="card shadow">
                    <div class="card-header rounded-0">
                        <div class="card-title">Item List</div>
                    </div>
                    <div class="card-body rounded-0">
                        <div class="container-fluid">
                            <div class="table-responsive">
                                <table class="table table-hover table-bordered table-stripped" id="order-item-tbl">
                                    <colgroup>
                                        <col width="5%">
                                        <col width="10%">
                                        <col width="40%">
                                        <col width="10%">
                                        <col width="17.5%">
                                        <col width="17.5%">
                                    </colgroup>
                                    <thead>
                                        <tr class="bg-gradient bg-dark-subtle">
                                            <th class="bg-transparent text-center border border-dark"></th>
                                            <th class="bg-transparent text-center border border-dark">Code</th>
                                            <th class="bg-transparent text-center border border-dark">Item Name</th>
                                            <th class="bg-transparent text-center border border-dark">QTY</th>
                                            
                                            <th class="bg-transparent text-center border border-dark">Price</th>
                                            <th class="bg-transparent text-center border border-dark">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="noData">
                                            <th class="text-center border-dark" colspan="6">No Item Listed Yet</th>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr class="bg-gradient bg-dark-subtle bg-opacity-50">
                                            <th class="bg-transparent text-center border border-dark" colspan="5">Sub-Total</th>
                                            <th class="bg-transparent border border-dark text-end" id="subTotalText">0</th>
                                        </tr>
                                        <tr class="bg-gradient bg-dark-subtle bg-opacity-50">
                                            <th class="bg-transparent text-center border border-dark" colspan="5">Discount (%)</th>
                                            <th class="bg-transparent border border-dark"><input type="number" class="form-control form-control-sm rounded-0 text-end" step="any" name="discount_percentage" id="discount_percentage" min="0" max="100" value="0"></th>
                                        </tr>
                                        <tr class="bg-gradient bg-dark-subtle bg-opacity-50">
                                            <th class="bg-transparent text-center border border-dark" colspan="5">Grand Total</th>
                                            <th class="bg-transparent border border-dark text-end" id="grandTotalText">0</th>
                                        </tr>
                                        <tr class="bg-gradient bg-dark-subtle bg-opacity-50">
                                            <th class="bg-transparent text-center border border-dark" colspan="5">Paid Amount</th>
                                            <th class="bg-transparent border border-dark"><input type="number" class="form-control form-control-sm rounded-0 text-end" step="any" name="tendered_amount" id="tendered_amount" min="0" value="0"></th>
                                        </tr>
                                        <tr class="bg-gradient bg-dark-subtle bg-opacity-50">
                                            <th class="bg-transparent text-center border border-dark" colspan="5">Pending Amount</th>
                                            <th class="bg-transparent border border-dark text-end" id="changeText">0</th>
                                        </tr>
                                        <tr class="bg-gradient bg-dark-subtle bg-opacity-50">
                                            <th class="bg-transparent text-center border border-dark" colspan="5">Pay Date</th>
                                            <th class="bg-transparent border border-dark"><input type="date" class="form-control form-control-sm rounded-0 text-end" step="any" name="paydate" id="paydate" required ></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            
                            <div class="d-flex justify-content-center w-100 my-3">
                                <button class="btn btm-sm rounded btn-primary" type="button" id="order-form-submit"><i class="fas fa-file-invoice"></i> Save & Generate Printable Invoice</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <?php include_once('settings-modal.php'); ?>
    
    <script src="assets/js/script.js"></script>
    <?php 
    if(isset($conn)){

    }
    ?>
    <?php if(isset($_SESSION['generate_receipt_id'])): ?>
    <script>
        setTimeout(function(){
            window.open("printable-receipt.php", "_blank", "width=900px,height=900px")
        },300)
    </script>
    <?php endif; ?>
</body>
</html>