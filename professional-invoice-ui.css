/* ===== PROFESSIONAL INVOICE UI - INDUSTRY LEVEL DESIGN ===== */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
    /* Modern Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #718096;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #f56565;
    --info-color: #4299e1;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f7fafc;
    --gray-100: #edf2f7;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #a0aec0;
    --gray-500: #718096;
    --gray-600: #4a5568;
    --gray-700: #2d3748;
    --gray-800: #1a202c;
    --gray-900: #171923;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--primary-gradient);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== MODERN NAVIGATION ===== */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.navbar-brand img {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    padding: 2px;
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600) !important;
    transition: var(--transition-fast);
    padding: var(--space-2) var(--space-4) !important;
    border-radius: var(--radius-lg);
    margin: 0 var(--space-1);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color) !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: translateY(-1px);
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: var(--transition-fast);
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

/* ===== MODERN CARDS ===== */
.modern-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.modern-card-header {
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
}

.modern-card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modern-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    position: relative;
    z-index: 1;
}

.modern-card-body {
    padding: var(--space-8);
}

/* ===== MODERN FORM CONTROLS ===== */
.modern-form-group {
    margin-bottom: var(--space-6);
}

.modern-label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.modern-input,
.form-control,
.form-select {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--white);
    font-family: var(--font-primary);
    position: relative;
}

.modern-input:focus,
.form-control:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.modern-input:read-only {
    background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
    color: var(--gray-600);
    cursor: not-allowed;
}

/* ===== SELECT2 MODERN STYLING ===== */
.select2-container--default .select2-selection--single {
    height: 52px !important;
    border: 2px solid var(--gray-200) !important;
    border-radius: var(--radius-lg) !important;
    background: var(--white) !important;
    transition: var(--transition-fast) !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 48px !important;
    padding-left: var(--space-4) !important;
    color: var(--gray-700) !important;
    font-weight: 500;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 48px !important;
    right: var(--space-4) !important;
}

.select2-container--default.select2-container--focus .select2-selection--single {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
}

.select2-dropdown {
    border: 2px solid var(--primary-color) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-xl) !important;
    backdrop-filter: blur(20px);
}

.select2-results__option {
    padding: var(--space-3) var(--space-4) !important;
    transition: var(--transition-fast) !important;
}

.select2-results__option--highlighted {
    background: var(--primary-gradient) !important;
    color: white !important;
}

/* ===== MODERN BUTTONS ===== */
.modern-btn,
.btn-primary,
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-6);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
}

.modern-btn::before,
.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.modern-btn:hover::before,
.btn-primary:hover::before {
    left: 100%;
}

.modern-btn:hover,
.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: white;
}

.modern-btn:active,
.btn-primary:active {
    transform: translateY(0);
}

.modern-btn-success {
    background: linear-gradient(135deg, var(--success-color), #38a169);
}

.modern-btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #e53e3e);
}

.modern-btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: 0.75rem;
}

/* ===== MODERN TABLE ===== */
.modern-table,
.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    margin-bottom: 0;
}

.modern-table thead th,
.table thead th {
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-5);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    border: none;
    position: relative;
}

.modern-table tbody td,
.table tbody td {
    padding: var(--space-4) var(--space-5);
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
    transition: var(--transition-fast);
}

.modern-table tbody tr:hover,
.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    transform: scale(1.01);
}

.modern-table tfoot th,
.table tfoot th {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
    padding: var(--space-4) var(--space-5);
    font-weight: 600;
    border-top: 2px solid var(--primary-color);
    color: var(--gray-700);
}

/* ===== INVOICE CODE DISPLAY ===== */
.invoice-code-display {
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    text-align: center;
    margin-bottom: var(--space-6);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.invoice-code-display::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.invoice-code-display .code {
    font-family: var(--font-mono);
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: 0.1em;
    position: relative;
    z-index: 1;
}

.invoice-code-display .label {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    z-index: 1;
}

/* ===== SECTION DIVIDERS ===== */
.section-divider {
    display: flex;
    align-items: center;
    margin: var(--space-8) 0;
    gap: var(--space-4);
}

.section-divider::before,
.section-divider::after {
    content: '';
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--gray-300), transparent);
}

.section-divider .title {
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
    box-shadow: var(--shadow-md);
}

/* ===== FLASH MESSAGES ===== */
.modern-alert,
.alert {
    padding: var(--space-5);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-6);
    border: none;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    backdrop-filter: blur(20px);
}

.modern-alert-success,
.alert-success {
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.1), rgba(56, 161, 105, 0.1));
    color: #2f855a;
    border-left: 4px solid var(--success-color);
}

.modern-alert-danger,
.alert-danger {
    background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(229, 62, 62, 0.1));
    color: #c53030;
    border-left: 4px solid var(--danger-color);
}

/* ===== PAGE HEADER ===== */
.page-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    margin-bottom: var(--space-8);
    color: white;
    box-shadow: var(--shadow-lg);
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: var(--space-2);
    background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    font-size: 1.125rem;
    opacity: 0.9;
}

/* ===== ANIMATIONS ===== */
.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .modern-card-body {
        padding: var(--space-5);
    }
    
    .modern-btn {
        width: 100%;
        justify-content: center;
    }
    
    .modern-table,
    .table {
        font-size: 0.875rem;
    }
    
    .modern-table thead th,
    .modern-table tbody td,
    .modern-table tfoot th,
    .table thead th,
    .table tbody td,
    .table tfoot th {
        padding: var(--space-2) var(--space-3);
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .invoice-code-display .code {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .modern-card-body {
        padding: var(--space-4);
    }
    
    .page-header {
        padding: var(--space-5);
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
}

/* ===== LOADING STATES ===== */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* ===== UTILITY CLASSES ===== */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-glow {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

/* ===== ENHANCED COMPONENTS ===== */

/* Modern Input Groups */
.input-group {
    position: relative;
    display: flex;
    align-items: stretch;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.input-group-text {
    background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
    border: 2px solid var(--gray-200);
    border-right: none;
    padding: var(--space-4);
    font-weight: 600;
    color: var(--gray-600);
    display: flex;
    align-items: center;
}

.input-group .modern-input,
.input-group .form-control {
    border-left: none;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

/* Enhanced Dropdown */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-2);
    margin-top: var(--space-2);
}

.dropdown-item {
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateX(4px);
}

/* Modern Badge */
.badge {
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

.bg-white {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-md);
}

/* Enhanced Table Interactions */
.table tbody tr {
    transition: var(--transition-fast);
    cursor: pointer;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    transform: translateX(2px);
    box-shadow: var(--shadow-md);
}

/* Remove Item Button Enhancement */
.remove-item {
    background: linear-gradient(135deg, var(--danger-color), #e53e3e);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.remove-item:hover {
    transform: scale(1.1) rotate(90deg);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #e53e3e, #c53030);
}

/* Enhanced Form Validation */
.modern-input:invalid,
.form-control:invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 4px rgba(245, 101, 101, 0.1);
}

.modern-input:valid,
.form-control:valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 4px rgba(72, 187, 120, 0.1);
}

/* Loading Button State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), #553c9a);
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: var(--transition-fast);
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1) rotate(90deg);
    box-shadow: var(--shadow-2xl);
}

/* Enhanced Typography */
.display-1, .display-2, .display-3, .display-4 {
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Progress Bar */
.progress {
    height: 8px;
    border-radius: var(--radius-lg);
    background: var(--gray-200);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: var(--primary-gradient);
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Modal */
.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    border-bottom: none;
    padding: var(--space-6);
}

.modal-body {
    padding: var(--space-6);
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    padding: var(--space-6);
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
}

/* Enhanced Tooltips */
[data-bs-toggle="tooltip"] {
    position: relative;
}

.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background: var(--gray-800);
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-3);
    box-shadow: var(--shadow-lg);
}

/* Enhanced Alerts with Icons */
.alert {
    position: relative;
    padding-left: 4rem;
}

.alert::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background-size: contain;
    background-repeat: no-repeat;
}

.alert-success::before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.alert-danger::before {
    content: '⚠';
    color: var(--danger-color);
    font-weight: bold;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== PRINT STYLES ===== */
@media print {
    body {
        background: white !important;
    }

    .modern-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .modern-btn,
    .btn,
    .fab {
        display: none !important;
    }

    .navbar {
        display: none !important;
    }

    .page-header {
        background: none !important;
        color: black !important;
    }
}
