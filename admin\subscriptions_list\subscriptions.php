<?php
// admin/subscriptions.php
require_once('../config.php');

// Check if user is admin
if (!$_settings->userdata('type') == 1) {
    header("Location: ../login.php");
    exit;
}

// Fetch all subscriptions
$subscriptions = [];
try {
    $query = "SELECT vs.id as subscription_id, vs.vendor_id, vs.purchase_date, vs.updated_at,
                     vs.active_status, u.username as vendor_name, u.email as vendor_email,
                     s.name as plan_name, s.price, s.expiry_days
              FROM vendor_subscriptions vs
              JOIN users u ON vs.vendor_id = u.id
              JOIN subscriptions s ON vs.subscription_id = s.id
              ORDER BY vs.purchase_date DESC";
    
    $result = $conn->query($query);
    while ($row = $result->fetch_assoc()) {
        // Calculate expiry date
        $start_date = new DateTime($row['updated_at'] > $row['purchase_date'] ? $row['updated_at'] : $row['purchase_date']);
        $expiry_date = clone $start_date;
        $expiry_date->add(new DateInterval('P' . $row['expiry_days'] . 'D'));
        $row['expiry_date'] = $expiry_date->format('Y-m-d H:i:s');
        
        $subscriptions[] = $row;
    }
} catch (Exception $e) {
    $error = "Failed to fetch subscriptions: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Subscriptions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-active {
            background-color: rgba(76, 201, 240, 0.1);
            color: #4cc9f0;
        }
        
        .status-pending {
            background-color: rgba(248, 150, 30, 0.1);
            color: #f8961e;
        }
        
        .status-rejected {
            background-color: rgba(247, 37, 133, 0.1);
            color: #f72585;
        }
        
        .status-expired {
            background-color: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }
    </style>
</head>
<body>
    <?php include('header.php'); ?>
    
    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">All Subscriptions</h2>
            <a href="subscription_approval.php" class="btn btn-primary">
                <i class="fas fa-clipboard-check"></i> Pending Approvals
            </a>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>Vendor</th>
                                <th>Plan</th>
                                <th>Price</th>
                                <th>Duration</th>
                                <th>Purchase Date</th>
                                <th>Expiry Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subscriptions as $sub): 
                                // Determine status
                                $today = new DateTime();
                                $expiry_date = new DateTime($sub['expiry_date']);
                                
                                if ($sub['active_status'] == 1 && $today > $expiry_date) {
                                    $status_class = "status-expired";
                                    $status_text = "Expired";
                                } elseif ($sub['active_status'] == 1) {
                                    $status_class = "status-active";
                                    $status_text = "Active";
                                } elseif ($sub['active_status'] == 0) {
                                    $status_class = "status-pending";
                                    $status_text = "Pending";
                                } else {
                                    $status_class = "status-rejected";
                                    $status_text = "Rejected";
                                }
                            ?>
                                <tr>
                                    <td>
                                        <div><?= htmlspecialchars($sub['vendor_name']) ?></div>
                                        <small class="text-muted"><?= htmlspecialchars($sub['vendor_email']) ?></small>
                                    </td>
                                    <td><?= htmlspecialchars($sub['plan_name']) ?></td>
                                    <td>$<?= number_format($sub['price'], 2) ?></td>
                                    <td><?= $sub['expiry_days'] ?> days</td>
                                    <td><?= date('M j, Y', strtotime($sub['purchase_date'])) ?></td>
                                    <td><?= date('M j, Y', strtotime($sub['expiry_date'])) ?></td>
                                    <td>
                                        <span class="status-badge <?= $status_class ?>"><?= $status_text ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>