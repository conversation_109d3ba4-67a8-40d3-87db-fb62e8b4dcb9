<?php if($_settings->chk_flashdata('success')): ?>
<script>
	alert_toast("<?php echo $_settings->flashdata('success') ?>",'success')
</script>
<?php endif;?>

<style>
    .img-avatar{
        width:45px;
        height:45px;
        object-fit:cover;
        object-position:center center;
        border-radius:100%;
    }

    /* General Box Styling */
    .user-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 15px;
    }

    .user-card {
        width: 24%;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 10px;
        background-color: #fff;
        text-align: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.3s ease;
    }

    .user-card:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .user-card .img-avatar {
        width: 60px;
        height: 60px;
        margin-bottom: 10px;
    }

    .user-card .name,
    .user-card .username,
    .user-card .type {
        font-size: 14px;
        margin: 5px 0;
    }

    .user-card .action {
        margin-top: 10px;
    }

    @media (max-width: 1200px) {
        .user-card {
            width: 32%;
        }
    }

    @media (max-width: 768px) {
        .user-card {
            width: 48%;
        }
    }

    @media (max-width: 576px) {
        .user-card {
            width: 100%;
        }
    }

    /* Table Adjustments */
    .table {
        display: none; /* Hide table for the new box layout */
    }
</style>

<div class="card card-outline card-primary">
	<div class="card-header">
		<h3 class="card-title">List of System Users</h3>
		<div class="card-tools">
			<a href="?page=user/manage_user" class="btn btn-flat btn-primary"><span class="fas fa-plus"></span>  Create New</a>
		</div>
	</div>
	<div class="card-body">
		<div class="container-fluid">
			<!-- New Box Layout -->
			<div class="user-box">
				<?php 
				$i = 1;
					$qry = $conn->query("SELECT *,concat(firstname,' ',lastname) as name from `users` where id != '1' and id != '{$_settings->userdata('id')}' and `type` != 3 order by concat(firstname,' ',lastname) asc ");
					while($row = $qry->fetch_assoc()):
				?>
					<div class="user-card">
						<img src="<?php echo validate_image($row['avatar']) ?>" class="img-avatar img-thumbnail p-0 border-2" alt="user_avatar">
						<p class="name"><?php echo ucwords($row['name']) ?></p>
						<p class="username m-0 truncate-1"><?php echo $row['username'] ?></p>
						<p class="type"><?php echo ($row['type'] == 1) ? 'Administrator' : 'Staff' ?></p>
						<div class="action">
							<button type="button" class="btn btn-flat btn-default btn-sm dropdown-toggle dropdown-icon" data-toggle="dropdown">
								Action
								<span class="sr-only">Toggle Dropdown</span>
							</button>
							<div class="dropdown-menu" role="menu">
								<a class="dropdown-item" href="?page=user/manage_user&id=<?php echo $row['id'] ?>"><span class="fa fa-edit text-primary"></span> Edit</a>
								<div class="dropdown-divider"></div>
								<a class="dropdown-item delete_data" href="javascript:void(0)" data-id="<?php echo $row['id'] ?>"><span class="fa fa-trash text-danger"></span> Delete</a>
							</div>
						</div>
					</div>
				<?php endwhile; ?>
			</div>
		</div>
	</div>
</div>

<script>
	$(document).ready(function(){
		$('.delete_data').click(function(){
			_conf("Are you sure to delete this User permanently?","delete_user",[$(this).attr('data-id')])
		})
		$('.table').dataTable();
	})
	function delete_user($id){
		start_loader();
		$.ajax({
			url:_base_url_+"classes/Users.php?f=delete",
			method:"POST",
			data:{id: $id},
			dataType:"json",
			error:err=>{
				console.log(err)
				alert_toast("An error occured.",'error');
				end_loader();
			},
			success:function(resp){
				if(typeof resp== 'object' && resp.status == 'success'){
					location.reload();
				}else{
					alert_toast("An error occured.",'error');
					end_loader();
				}
			}
		})
	}
</script>
