

<?php
require_once('./../../config.php');

$invoices = [];
// Variables to calculate totals
$total_total_amount = 0;
$total_discount_amount = 0;
$total_paid_amount = 0;
$total_pending_amount = 0;

// Check if the customer ID is provided
if (!isset($_GET['customer']) || empty($_GET['customer'])) {
    echo "No customer ID provided.";
    exit;
}

$customer_id = $conn->real_escape_string($_GET['customer']); // Secure the customer ID

echo "<br>";
// SQL query to fetch invoices for the customer
$qry = $conn->query("
    SELECT 
        p.id, 
        p.invoice_code, 
        p.customer,
        p.cashier, 
        p.total_amount, 
        p.discount_percentage, 
        p.discount_amount, 
        p.tendered_amount, 
        p.created_at,
        p.updated_at
    FROM
        `invoices_tbl` p
   
    WHERE 
        p.customer = '$customer_id'
    ORDER BY p.created_at DESC
");

// Check for query errors
if (!$qry) {
    echo "Error fetching invoices: " . $conn->error;
    exit;
}

// If invoices exist, process them
if ($qry->num_rows > 0) {
    while ($row = $qry->fetch_assoc()) {
        $invoices[] = $row; // Store each invoice in the array
        // Accumulate totals
        $total_total_amount += $row['total_amount'];
        $total_discount_amount += $row['discount_amount'];
        $total_paid_amount += $row['tendered_amount'];
        $total_pending_amount += ($row['total_amount'] - $row['tendered_amount'] - $row['discount_amount']);
    }
} else {
    echo "<center>No invoices found for this customer.</center>";
    exit;
}
?>

<!-- Styles -->
<style>
 body {
	font-family: Arial, sans-serif;
}


.container {
	width: 100%;
	padding: 20px;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.title {
	font-size: 1.5rem;
	color: #333;
}

.tools {
	margin-left: auto;
}

.btn {
	background-color: #007bff;
	color: white;
	padding: 10px 20px;
	border: none;
	border-radius: 5px;
	text-decoration: none;
	font-size: 14px;
}

.btn:hover {
	background-color: #0056b3;
}

.table-container {
	width: 100%;
	overflow-x: auto;
}

table {
	width: 100%;
	border-collapse: collapse;
}

th, td {
	padding: 10px;
	text-align: left;
	border-bottom: 1px solid #ddd;
}

th {
	background-color: #f8f9fa;
	color: #333;
	font-weight: bold;
}

tr:nth-child(even) {
	background-color: #f2f2f2;
}

.badge {
	display: inline-block;
	padding: 5px 10px;
	border-radius: 15px;
	font-size: 12px;
	color: white;
}

.badge-success {
	background-color: #28a745;
}

.badge-danger {
	background-color: #dc3545;
}

.btn-flat {
	padding: 5px 10px;
	border-radius: 5px;
}

.dropdown-menu {
	background-color: #fff;
	border: 1px solid #ddd;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
	padding: 10px 15px;
	color: #333;
	text-decoration: none;
	display: block;
}

.dropdown-item:hover {
	background-color: #f8f9fa;
}

@media (max-width: 768px) {
	.container {
		padding: 10px;
	}

	.header {
		flex-direction: column;
		align-items: flex-start;
	}

	.table-container {
		overflow-x: auto;
	}
}
</style>




<div class="container">



<!-- Invoice Details HTML -->
<div class="container-fluid invoice-details">
  <?php echo '<h4 class="text-center">Customer Invoice List</h4>';
echo "Customer Name : - ". $customer_id;?>
</div>
<div class="table-container">
    <table class="table table-bordered table-stripped">
    <colgroup>
				<col width="20%">
				<col width="15%">
				<col width="20%">
				<col width="15%">
				<col width="15%">
				<col width="15%">
				<col width="15%">
			</colgroup>    
    <thead>
            <tr class="bg-gradient-secondary">
                <th>Date</th>
                <th>Invoice Code</th>
                <th>Total Amount (₹)</th>
                <th>Discount Amount (₹)</th>
                <th>Paid Amount (₹)</th>
                <th>Pending Amount (₹)</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($invoices as $invoice): ?>
                <?php 
                    // Calculate the pending amount for each invoice
                    $pending = $invoice['total_amount'] - $invoice['tendered_amount'] - $invoice['discount_amount'];
                ?>
                <tr>
                    <td class="text-center"><?= isset($invoice['created_at']) ? $invoice['created_at'] : "" ?></td>
                    <td class="text-center"><?= isset($invoice['invoice_code']) ? $invoice['invoice_code'] : "" ?></td>
                    <td class="text-right"><?= number_format($invoice['total_amount'], 2) ?>₹</td>
                    <td class="text-right"><?= number_format($invoice['discount_amount'], 2) ?>₹</td>
                    <td class="text-right"><?= number_format($invoice['tendered_amount'], 2) ?>₹</td>
                    <td class="text-center"><?= number_format($pending, 2) ?>₹</td>
                    <td class="text-center">
                        <?php if ($invoice['tendered_amount'] == $invoice['total_amount'] || $pending == 0): ?>
                            <span class="badge badge-success">Paid</span>
                        <?php elseif ($invoice['tendered_amount'] == 0): ?>
                            <span class="badge badge-danger">Unpaid</span>
                        <?php elseif ($invoice['tendered_amount'] > 0 && $pending > 0): ?>
                            <span class="badge badge-warning">Partial</span>
                        <?php else: ?>
                            <span class="badge badge-secondary">Pending</span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
        <tfoot>
            <tr>
                <th colspan="2" class="text-right">Total</th>
                <th class="text-center"><?= number_format($total_total_amount, 2) ?>₹</th>
                <th class="text-center"><?= number_format($total_discount_amount, 2) ?>₹</th>
                <th class="text-center"><?= number_format($total_paid_amount, 2) ?>₹</th>
                <th class="text-center"><?= number_format($total_pending_amount, 2) ?>₹</th>
                <th></th>
            </tr>
        </tfoot>
    </table>
    
   
</div>
</div></div>
 <div class="text-right mt-3">
        <button class="btn btn-default" type="button" data-dismiss="modal"><i class="fa fa-times"></i> Close</button>
        <button class="btn btn-primary" type="button" onclick="window.print();"><i class="fa fa-print"></i> Print</button>
    </div>
<script>
	$(document).ready(function(){
	
		$('table th,table td').addClass('align-middle px-2 py-1')
		$('.table').dataTable();
	})

</script>
