<?php
// Start session to ensure only logged-in admins can access this page (optional)

// Uncomment below if you want to restrict access to logged-in admins
// if (!isset($_SESSION['admin_logged_in'])) {
//     header("Location: login.php"); // Redirect to login page if not logged in
//     exit();
// }

require_once('../config.php');

// Query to fetch all records from the change_requests table
$sql = "SELECT vendor_id, fullName, email, phone, address, details, request_date FROM change_requests";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Change Requests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
        }

        .container {
            width: 90%;
            margin: 20px auto;
            max-width: 1200px;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        .box-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .box {
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            width: calc(100% / 1 - 20px); /* Full width for single column */
            box-sizing: border-box;
        }

        .box h2 {
            margin-top: 0;
            font-size: 1.2em;
            color: #333;
        }

        .box p {
            margin: 5px 0;
            color: #666;
        }

        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
            .box-container {
                flex-direction: column;
                align-items: stretch;
            }

            .box {
                width: 100%; /* Full width on small screens */
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Admin Panel - Change Requests</h1>

        <div class="box-container">
            <?php
            if ($result->num_rows > 0) {
                // Output data of each row
                while ($row = $result->fetch_assoc()) {
                    echo "<div class='box'>";
                    echo "<h2>Request ID: " . $row["vendor_id"] . "</h2>";
                    echo "<p><strong>Full Name:</strong> " . $row["fullName"] . "</p>";
                    echo "<p><strong>Email:</strong> " . $row["email"] . "</p>";
                    echo "<p><strong>Phone:</strong> " . $row["phone"] . "</p>";
                    echo "<p><strong>Address:</strong> " . $row["address"] . "</p>";
                    echo "<p><strong>Details:</strong> " . $row["details"] . "</p>";
                    echo "<p><strong>Request Date:</strong> " . $row["request_date"] . "</p>";
                    echo "</div>";
                }
            } else {
                echo "<div class='box'><p>No requests found</p></div>";
            }
            ?>
        </div>
    </div>
</body>
</html>
