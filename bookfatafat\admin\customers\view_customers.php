<?php
require_once('./../../config.php');
if(isset($_GET['id']) && $_GET['id'] > 0){
    $qry = $conn->query("
        SELECT 
            p.id, 
            p.name, 
            p.mobileno, 
            p.altmobileno, 
            p.email, 
            p.description, 
            p.adharno, 
            p.image_path, 
            p.status 
        FROM 
            `customers_list` p 
        WHERE 
            p.id = '{$_GET['id']}' 
            AND p.delete_flag = 0
    ");
    
    if($qry->num_rows > 0){
        foreach($qry->fetch_assoc() as $k => $v){
            $$k = $v;
        }
    }else{
?>
        <center>Unknown Customer</center>
        <style>
            #uni_modal .modal-footer{
                display:none
            }
        </style>
        <div class="text-right">
            <button class="btn btn-default bg-gradient-dark btn-flat" data-dismiss="modal"><i class="fa fa-times"></i> Close</button>
        </div>
        <?php
        exit;
    }
}
?>
<style>
    #uni_modal .modal-footer{
        display:none
    }
    #cust-img-view{
        width:15em;
        max-height:20em;
        object-fit:scale-down;
        object-position: center center;
    }
</style>
<div class="container-fluid">
    <center><img src="<?= validate_image(isset($image_path) ? $image_path : "") ?>" alt="Customer Image" class="img-thumbnail p-0 bg-gradient-gray" id="cust-img-view"></center>
    <dl>
        <dt class="text-muted">Name</dt>
        <dd class="pl-3"><?= isset($name) ? $name : "" ?></dd>
        <dt class="text-muted">Mobile Number</dt>
        <dd class="pl-3"><?= isset($mobileno) ? $mobileno : "" ?></dd>
        <dt class="text-muted">Alternate Mobile Number</dt>
        <dd class="pl-3"><?= isset($altmobileno) ? $altmobileno : "" ?></dd>
        <dt class="text-muted">Email</dt>
        <dd class="pl-3"><?= isset($email) ? $email : "" ?></dd>
        <dt class="text-muted">Description</dt>
        <dd class="pl-3"><?= isset($description) ? html_entity_decode($description) : "" ?></dd>
        <dt class="text-muted">Aadhar Number</dt>
<dd class="pl-3"><?= maskAadharNumber($adharno) ?></dd>

        <dt class="text-muted">Status</dt>
        <dd class="pl-3">
            <?php if(isset($status) && $status == 1): ?>
                <span class="badge badge-success bg-gradient-success px-3 rounded-pill">Active</span>
            <?php else: ?>
                <span class="badge badge-danger bg-gradient-danger px-3 rounded-pill">Inactive</span>
            <?php endif; ?>
        </dd>
    </dl>
    <div class="clear-fix mb-3"></div>
    <div class="text-right">
        <button class="btn btn-default bg-gradient-dark btn-sm btn-flat" type="button" data-dismiss="modal"><i class="fa fa-times"></i> Close</button>
    </div>
</div>
<?php
function maskAadharNumber($adharno) {
    // Check if Aadhar number is set and is numeric
    if(isset($adharno) && is_numeric($adharno)) {
        // Mask the Aadhar number
        $masked_number = str_repeat('*', strlen($adharno) - 4) . substr($adharno, -4);
        return $masked_number;
    } else {
        return ""; // Handle case where Aadhar number is not set or not numeric
    }
}
?>
