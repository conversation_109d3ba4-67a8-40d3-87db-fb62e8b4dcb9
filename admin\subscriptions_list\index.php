<?php
// admin/subscription_approval.php
require_once('../config.php');

// Check if user is admin
if (!$_settings->userdata('type') == 1) {
    header("Location: ../login.php");
    exit;
}

// Function to update subscription status
function updateSubscriptionStatus($conn, $subscription_id, $status) {
    $query = "UPDATE vendor_subscriptions SET active_status = ? WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $status, $subscription_id);
    return $stmt->execute();
}

// Handle approval/rejection
if (isset($_POST['action']) && isset($_POST['subscription_id'])) {
    $subscription_id = (int)$_POST['subscription_id'];
    $action = $_POST['action'];
    
    try {
        if ($action == 'approve') {
            $success = updateSubscriptionStatus($conn, $subscription_id, 1);
            $message = $success ? "Subscription approved successfully!" : "Failed to approve subscription.";
        } elseif ($action == 'reject') {
            $success = updateSubscriptionStatus($conn, $subscription_id, 2);
            $message = $success ? "Subscription rejected successfully!" : "Failed to reject subscription.";
        }
        
        if (isset($success)) {
            $_SESSION['flash_message'] = [
                'type' => $success ? 'success' : 'danger',
                'message' => $message
            ];
        }
    } catch (Exception $e) {
        $_SESSION['flash_message'] = [
            'type' => 'danger',
            'message' => "Error: " . $e->getMessage()
        ];
    }
    
    header("Location: subscription_approval.php");
    exit;
}

// Fetch pending subscriptions
$pending_subscriptions = [];
$all_subscriptions = [];
$subscription_stats = [
    'total' => 0,
    'active' => 0,
    'pending' => 0,
    'rejected' => 0,
    'expired' => 0
];

try {
    // Fetch pending subscriptions
    $query = "SELECT vs.id as subscription_id, vs.vendor_id, vs.purchase_date,vs.payment_id, 
                     u.username as vendor_name, u.username as vendor_email,
                     s.name as plan_name, s.price, s.expiry_days
              FROM vendor_subscriptions vs
              JOIN vendor_list u ON vs.vendor_id = u.id
              JOIN subscriptions s ON vs.subscription_id = s.id
              WHERE vs.active_status = 0
              ORDER BY vs.updated_at ASC";
    
    $result = $conn->query($query);
    while ($row = $result->fetch_assoc()) {
        $pending_subscriptions[] = $row;
    }
    
    // Fetch all subscriptions for the table
    $query_all = "SELECT vs.id as subscription_id, vs.vendor_id, vs.purchase_date, 
                         u.username as vendor_name, u.username as vendor_email,
                         s.name as plan_name, s.price, s.expiry_days, vs.active_status,
                         DATE_ADD(vs.purchase_date, INTERVAL s.expiry_days DAY) as expiry_date
                  FROM vendor_subscriptions vs
                  JOIN vendor_list u ON vs.vendor_id = u.id
                  JOIN subscriptions s ON vs.subscription_id = s.id
                  ORDER BY vs.updated_at DESC";
    
    $result_all = $conn->query($query_all);
    while ($row = $result_all->fetch_assoc()) {
        $all_subscriptions[] = $row;
    }
    
    // Get subscription statistics
    $stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(active_status = 1) as active,
                    SUM(active_status = 0) as pending,
                    SUM(active_status = 2) as rejected,
                    SUM(active_status = 3) as expired
                    FROM vendor_subscriptions";
    
    $stats_result = $conn->query($stats_query);
    if ($stats_row = $stats_result->fetch_assoc()) {
        $subscription_stats = [
            'total' => $stats_row['total'],
            'active' => $stats_row['active'],
            'pending' => $stats_row['pending'],
            'rejected' => $stats_row['rejected'],
            'expired' => $stats_row['expired']
        ];
    }
    
} catch (Exception $e) {
    $error = "Failed to fetch subscriptions: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Approvals</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4361ee;
            --primary-dark: #3a56d4;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --warning: #f8961e;
            --danger: #f72585;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table thead {
            background-color: var(--primary);
            color: white;
        }
        
        .badge-pending {
            background-color: var(--warning);
        }
        
        .badge-active {
            background-color: var(--success);
        }
        
        .badge-rejected {
            background-color: var(--danger);
        }
        
        .badge-expired {
            background-color: #6c757d;
        }
        
        .action-btns .btn {
            min-width: 100px;
        }
        
        .stats-card {
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <?php include('header.php'); ?>
    
    <div class="container py-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">Subscription Record</h2>
            
        </div>
        
        <?php if (isset($_SESSION['flash_message'])): ?>
            <div class="alert alert-<?= $_SESSION['flash_message']['type'] ?> alert-dismissible fade show">
                <?= $_SESSION['flash_message']['message'] ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php unset($_SESSION['flash_message']); ?>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>
        
        <!-- Subscription Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">Total</h5>
                        <h2 class="mb-0"><?= $subscription_stats['total'] ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">Active</h5>
                        <h2 class="mb-0"><?= $subscription_stats['active'] ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5 class="card-title">Pending</h5>
                        <h2 class="mb-0"><?= $subscription_stats['pending'] ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-danger text-white">
                    <div class="card-body">
                        <h5 class="card-title">Rejected/Expired</h5>
                        <h2 class="mb-0"><?= $subscription_stats['rejected'] + $subscription_stats['expired'] ?></h2>
                    </div>
                </div>
            </div>
        </div>
        
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">Pending Subscription Approvals</h2>
          
        </div>
        <?php if (empty($pending_subscriptions)): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                    <h3>No Pending Subscriptions</h3>
                    <p class="text-muted">All subscriptions have been processed.</p>
                </div>
            </div>
        <?php else: ?>
            <div class="card mb-5">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Pending Approvals</h4>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Vendor</th>
                                    
                                    <th>Plan</th>
                                    <th>Price</th>
                                    <th>Payment Id</th>
                                    <th>Purchase Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_subscriptions as $sub): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($sub['vendor_name']) ?></td>
                                        
                                        <td><?= htmlspecialchars($sub['plan_name']) ?></td>
                                        <td>$<?= number_format($sub['price'], 2) ?></td>
                                        <td><?= $sub['payment_id'] ?> </td>
                                        <td><?= date('M j, Y', strtotime($sub['purchase_date'])) ?></td>
                                        <td>
                                            <span class="badge badge-pending text-white">Pending</span>
                                        </td>
                                        <td class="action-btns">
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="subscription_id" value="<?= $sub['subscription_id'] ?>">
                                                <button type="submit" name="action" value="approve" class="btn btn-success btn-sm">
                                                    <i class="fas fa-check"></i> Approve
                                                </button>
                                            </form>
                                            <br><br>
                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="subscription_id" value="<?= $sub['subscription_id'] ?>">
                                                <button type="submit" name="action" value="reject" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-times"></i> Reject
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- All Subscriptions Table -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">All Subscription Records</h4>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Vendor</th>
                                <th>Plan</th>
                                <th>Price</th>
                                <th>Duration</th>
                                <th>Purchase Date</th>
                                <th>Expiry Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($all_subscriptions as $sub): 
                                $expiry_date = date('M j, Y', strtotime($sub['expiry_date']));
                                $is_expired = strtotime($sub['expiry_date']) < time() && $sub['active_status'] == 1;
                                $status_class = '';
                                $status_text = '';
                                
                                if ($sub['active_status'] == 1) {
                                    $status_class = $is_expired ? 'bg-secondary' : 'bg-success';
                                    $status_text = $is_expired ? 'Expired' : 'Active';
                                } elseif ($sub['active_status'] == 0) {
                                    $status_class = 'bg-warning';
                                    $status_text = 'Pending';
                                } elseif ($sub['active_status'] == 2) {
                                    $status_class = 'bg-danger';
                                    $status_text = 'Rejected';
                                }
                            ?>
                                <tr>
                                    <td><?= htmlspecialchars($sub['vendor_name']) ?></td>
                                    <td><?= htmlspecialchars($sub['plan_name']) ?></td>
                                    <td>$<?= number_format($sub['price'], 2) ?></td>
                                    <td><?= $sub['expiry_days'] ?> days</td>
                                    <td><?= date('M j, Y', strtotime($sub['purchase_date'])) ?></td>
                                    <td><?= $expiry_date ?></td>
                                    <td>
                                        <span class="badge <?= $status_class ?> text-white"><?= $status_text ?></span>
                                        <?php if ($is_expired): ?>
                                            <span class="badge bg-dark text-white">Expired</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                new bootstrap.Alert(alert).close();
            });
        }, 5000);
    </script>
</body>
</html>